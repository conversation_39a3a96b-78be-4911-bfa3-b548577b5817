<template>
  <div class="dashboard-container">
    <!-- 背景图片 -->
    <img src="@/assets/images/dashboard/banner.png" alt="" class="dashboard-bg" />

    <div
      class="bg-gradient-to-r from-slate-900 to-transparent absolute inset-y-0 left-0 w-2/3"
    ></div>

    <div
      class="bg-gradient-to-l from-slate-900 to-transparent absolute inset-y-0 right-0 w-2/3"
    ></div>

    <!-- 主要内容 -->
    <div class="dashboard-content h-full overflow-auto">
      <!-- 顶部栏 -->
      <DashboardHeader @toggle-fullscreen="toggleFullscreen" />

      <!-- 主体内容区域 -->
      <div class="dashboard-main">
        <!-- 上半部分：数据可视化区域 -->
        <div class="dashboard-top">
          <!-- 左侧模块 -->
          <div class="dashboard-left">
            <LeftSidePanel />
          </div>

          <!-- 中间核心模块 -->
          <div class="dashboard-center">
            <CenterPanel />
          </div>

          <!-- 右侧模块 -->
          <div class="dashboard-right">
            <RightSidePanel />
          </div>
        </div>

        <!-- 下半部分：AR眼镜实时画面 -->
        <!-- <div class="dashboard-bottom">
          <ARGlassesPanel />
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import DashboardHeader from './components/DashboardHeader.vue';
  import LeftSidePanel from './components/LeftSidePanel.vue';
  import CenterPanel from './components/CenterPanel.vue';
  import RightSidePanel from './components/RightSidePanel.vue';
  import ARGlassesPanel from './components/ARGlassesPanel.vue';

  const isFullscreen = ref(false);

  // 切换全屏
  function toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      isFullscreen.value = true;
    } else {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
</script>

<style scoped>
  /* 响应式布局 */
  @media (max-width: 1400px) {
    .dashboard-top {
      grid-template-columns: 1fr 1.5fr 1fr;
    }
  }

  @media (max-width: 1200px) {
    .dashboard-top {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto;
    }

    .dashboard-bottom {
      height: 250px;
    }
  }

  .dashboard-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  .dashboard-bg {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .dashboard-content {
    display: flex;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 20px;
  }

  .dashboard-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-top: 20px;
    gap: 20px;
  }

  .dashboard-top {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    flex: 1;
    min-height: 0;
    gap: 20px;
  }

  .dashboard-left,
  .dashboard-center,
  .dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .dashboard-bottom {
    flex-shrink: 0;
    height: 300px;
  }
</style>
